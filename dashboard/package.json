{"name": "dashboard", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts"}, "dependencies": {"@gd/core": "workspace:*", "@gd/firestore-rest": "workspace:*", "@opennextjs/cloudflare": "^1.2.1", "@reduxjs/toolkit": "^2.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "wrangler": "^4.19.1"}}