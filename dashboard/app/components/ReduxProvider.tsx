"use client";
import { useRef } from "react";
import { Provider } from "react-redux";
import { makeStore, type AppStore } from "@gd/core";

type ReduxProviderProps = {
  children: React.ReactNode;
};

/**
 * Redux Provider wrapper for the dashboard application
 * Creates a unique store instance for each component tree
 * Ensures proper SSR/CSR compatibility
 */
export const ReduxProvider = ({ children }: ReduxProviderProps) => {
  const storeRef = useRef<AppStore | null>(null);

  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore();
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
};
