# Redux Toolkit + RTK Query Authentication System

## Overview

This document describes the complete Redux-based authentication system that replaces the previous Pinia implementation. The system provides type-safe, SSR/CSR compatible authentication with automatic state management and API integration.

## Architecture

### Core Components

1. **Redux Store** (`packages/lcCore/src/store/`)
   - `authSlice.ts` - Authentication state management
   - `authApiSlice.ts` - RTK Query API endpoints
   - `authThunks.ts` - Async authentication operations
   - `index.ts` - Store configuration with middleware

2. **AuthProvider** (`packages/lcCore/src/providers/AuthProvider.tsx`)
   - Integrates Redux state with Firebase authentication
   - Maintains backward compatibility with existing useAuth hook
   - Handles automatic auth status checking on app load

3. **Login Page** (`dashboard/app/login/page.tsx`)
   - Two-step authentication flow (email/password → OTP)
   - Real-time error handling and loading states
   - Automatic redirection after successful authentication

4. **Redux Provider** (`dashboard/app/components/ReduxProvider.tsx`)
   - SSR/CSR compatible store initialization
   - Proper hydration and state management

## Authentication Flow

### 1. Login Process
```typescript
// User enters email/password
dispatch(submitLoginForm({ email, password, otpChannel: 'email' }))
  .unwrap()
  .then(() => {
    // Redux updates currentPage to 'otp'
    // User sees OTP input form
  });
```

### 2. OTP Verification
```typescript
// User enters OTP code
dispatch(submitOtp(otpCode))
  .unwrap()
  .then(() => {
    // Authentication data stored in Redux and localStorage
    // Firebase authentication completed
    // User redirected to dashboard
  });
```

### 3. Logout Process
```typescript
// User clicks logout
dispatch(logoutUser())
  .unwrap()
  .then(() => {
    // All auth state cleared
    // User redirected to login
  });
```

## State Management

### Redux State Structure
```typescript
interface AuthSliceState {
  user: User | null;                    // Firebase user object
  isAuthenticated: boolean;             // Authentication status
  isAuthLoading: boolean;              // Initial auth check loading
  currentPage: "login" | "otp";        // Current auth page
  isLoading: boolean;                  // Operation in progress
  loginError: string | null;           // Error messages
  loginData: ILoginData | null;        // Form data storage
  authData: IAuthData | null;          // Authentication tokens
}
```

### Available Actions
```typescript
// Sync actions
setUser(user)
setIsAuthenticated(boolean)
setIsAuthLoading(boolean)
setCurrentPage("login" | "otp")
setIsLoading(boolean)
setLoginError(string | null)
setLoginData(ILoginData | null)
setAuthData(IAuthData | null)
setAuth({ authData, user })
clearAuth()

// Async thunks
submitLoginForm(credentials)
submitOtp(otpCode)
logoutUser()
checkAuthStatus()
```

## RTK Query Endpoints

### Authentication API
```typescript
// Login endpoint
useSubmitLoginMutation()

// OTP verification
useSubmitOtpMutation()

// Token refresh
useRefreshTokenMutation()

// Logout
useLogoutMutation()
```

## Usage Examples

### Using Redux Hooks in Components
```typescript
import { useAppDispatch, useAppSelector, submitLoginForm } from '@gd/core';

function LoginComponent() {
  const dispatch = useAppDispatch();
  const { isLoading, loginError } = useAppSelector(state => state.auth);
  
  const handleLogin = async (credentials) => {
    try {
      await dispatch(submitLoginForm(credentials)).unwrap();
      // Handle success
    } catch (error) {
      // Error is automatically stored in Redux state
    }
  };
}
```

### Using RTK Query Hooks
```typescript
import { useSubmitLoginMutation } from '@gd/core';

function LoginComponent() {
  const [submitLogin, { isLoading, error }] = useSubmitLoginMutation();
  
  const handleLogin = async (credentials) => {
    try {
      const result = await submitLogin(credentials).unwrap();
      // Handle success
    } catch (error) {
      // Handle error
    }
  };
}
```

### Accessing Auth State
```typescript
import { useAuth, useAppSelector } from '@gd/core';

function MyComponent() {
  // Using existing AuthProvider (backward compatible)
  const { isAuthenticated, user, logout } = useAuth();
  
  // Using Redux directly (more detailed state)
  const { currentPage, loginError, authData } = useAppSelector(state => state.auth);
}
```

## Device Information

The system automatically collects device information for authentication:

```typescript
interface DeviceInfo {
  deviceId: string;      // Unique browser fingerprint
  deviceName?: string;   // Browser name
  deviceType?: string;   // mobile/tablet/desktop
  domain?: string;       // Current domain
}
```

## Token Management

### Automatic Storage
- **Cookies**: Basic auth info for server-side access
- **localStorage**: Complete authentication data
- **Redux State**: Runtime authentication state

### Token Structure
```typescript
interface IAuthData {
  firebaseToken: string;   // Firebase custom token
  apiKey: string;         // LC API key
  userId: string;         // User identifier
  companyId: string;      // Company identifier
  jwt: string;           // JWT token
  refreshJwt: string;     // JWT refresh token
  authToken: string;      // Primary auth token
  refreshToken: string;   // Auth refresh token
}
```

## SSR/CSR Compatibility

### Store Initialization
```typescript
// In app layout
<ReduxProvider>
  <ClientAuthProvider>
    {children}
  </ClientAuthProvider>
</ReduxProvider>
```

### Server-Side Considerations
- Store is created per request to prevent state pollution
- Authentication state is checked on client hydration
- Proper loading states prevent hydration mismatches

## Error Handling

### Automatic Error Management
- API errors are automatically caught and stored in Redux state
- User-friendly error messages are displayed
- Failed operations don't crash the application

### Error Recovery
- Invalid tokens are automatically cleared
- Users are redirected to login on authentication failures
- Graceful fallbacks for network issues

## Migration from Pinia

### What Changed
- ✅ Pinia store replaced with Redux Toolkit
- ✅ Vue composables replaced with React hooks
- ✅ State management unified under Redux
- ✅ RTK Query replaces manual API calls

### What Stayed the Same
- ✅ AuthProvider interface unchanged
- ✅ useAuth hook still works
- ✅ AuthGuard functionality preserved
- ✅ Authentication flow identical
- ✅ Token storage mechanism maintained

## Testing

### Manual Testing
1. Visit http://localhost:3001
2. Should redirect to /login (unauthenticated)
3. Enter email/password → should show OTP form
4. Enter OTP → should redirect to dashboard
5. Logout → should redirect to login

### Development Tools
- Redux DevTools for state inspection
- Network tab for API call monitoring
- Console for error debugging

## Production Considerations

### Security
- Tokens are properly stored and managed
- Device fingerprinting for additional security
- Automatic token refresh (when implemented)

### Performance
- RTK Query provides automatic caching
- Optimistic updates for better UX
- Minimal re-renders with proper selectors

### Monitoring
- All authentication events are logged
- Error states are properly tracked
- User flows are observable through Redux DevTools
