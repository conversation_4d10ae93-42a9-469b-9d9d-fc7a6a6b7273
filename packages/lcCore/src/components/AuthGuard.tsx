"use client";
import { useEffect, type ReactNode } from "react";
import { useAuth } from "../providers/AuthProvider";

type AuthGuardProps = {
  children: ReactNode;
  redirectTo?: string;
  fallback?: ReactNode;
  onRedirect?: (redirectTo: string) => void;
  showLoadingMessage?: boolean;
};

/**
 * AuthGuard component that protects routes by checking authentication status
 * Calls onRedirect callback for unauthenticated users (allows custom redirect logic)
 */
const AuthGuard = ({
  children,
  redirectTo = "/login",
  fallback,
  onRedirect,
  showLoadingMessage = true
}: AuthGuardProps) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Default fallback with better styling
  const defaultFallback = (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        {showLoadingMessage && (
          <p className="text-gray-600">Checking authentication...</p>
        )}
      </div>
    </div>
  );

  const loadingComponent = fallback || defaultFallback;

  useEffect(() => {
    // Only redirect if we're done loading and user is not authenticated
    if (!isLoading && !isAuthenticated && onRedirect) {
      onRedirect(redirectTo);
    }
  }, [isAuthenticated, isLoading, onRedirect, redirectTo]);

  // Show loading state while checking authentication
  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  // Show loading state while redirecting
  if (!isAuthenticated) {
    return <>{loadingComponent}</>;
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default AuthGuard;
