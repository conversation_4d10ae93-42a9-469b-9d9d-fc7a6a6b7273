import { AuthProvider, useAuth } from "./providers";
import { AuthGuard } from "./components";
import {
  makeStore,
  useAppDispatch,
  useAppSelector,
  useAppStore,
  type RootState,
  type AppStore,
  type AppDispatch
} from "./store";
import { getFirstZodError, getLCRequestCommonHeaders, getDevice } from "./utils";
import { lcBackendRequest } from "./lib";
import { lcAuthRequest, type ILoginFormPayload } from "./store/features/auth/authAPI";
import { lcFbSignInWithCustomToken } from "./firebase";

// Redux exports
import {
  setUser,
  setIsAuthenticated,
  setIsAuthLoading,
  setCurrentPage,
  setIsLoading,
  setLoginError,
  setLoginData,
  setAuthData,
  setAuth,
  clearAuth,
  convertUserToSerializable,
  type IAuthData,
  type ILoginData,
  type ISerializableUser
} from "./store/features/auth/authSlice";

import {
  useSubmitLoginMutation,
  useSubmitOtpMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
  type LoginResponse,
  type OtpResponse
} from "./store/features/auth/authApiSlice";

import {
  submitLoginForm,
  submitOtp,
  logoutUser,
  checkAuthStatus
} from "./store/features/auth/authThunks";

export {
  // Auth Provider & Components
  AuthProvider,
  useAuth,
  AuthGuard,

  // Redux Store
  makeStore,
  useAppDispatch,
  useAppSelector,
  useAppStore,

  // Redux Actions
  setUser,
  setIsAuthenticated,
  setIsAuthLoading,
  setCurrentPage,
  setIsLoading,
  setLoginError,
  setLoginData,
  setAuthData,
  setAuth,
  clearAuth,

  // Redux Thunks
  submitLoginForm,
  submitOtp,
  logoutUser,
  checkAuthStatus,

  // RTK Query Hooks
  useSubmitLoginMutation,
  useSubmitOtpMutation,
  useRefreshTokenMutation,
  useLogoutMutation,

  // Utilities
  getFirstZodError,
  getLCRequestCommonHeaders,
  getDevice,
  lcBackendRequest,
  lcAuthRequest,
  lcFbSignInWithCustomToken,
  convertUserToSerializable,

  // Types
  type ILoginFormPayload,
  type IAuthData,
  type ILoginData,
  type ISerializableUser,
  type LoginResponse,
  type OtpResponse,
  type RootState,
  type AppStore,
  type AppDispatch
};