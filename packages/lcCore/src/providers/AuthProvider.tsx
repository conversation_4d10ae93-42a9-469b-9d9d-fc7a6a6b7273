"use client";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { lcFbAuth, lcFbOnAuthStateChanged } from "@/firebase";
import { setUser, setIsAuthenticated, setIsAuthLoading, clearAuth } from "@/store/features/auth/authSlice";
import { logoutUser, checkAuthStatus } from "@/store/features/auth/authThunks";
import type { User } from "firebase/auth";
import type { RootState, AppDispatch } from "@/store";

type AuthContextType = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: () => void;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function AuthProvider({ children }: { children: ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();

  // Get auth state from Redux (serializable user data)
  const { user: serializableUser, isAuthenticated, isAuthLoading } = useSelector((state: RootState) => state.auth);

  // Local state for Firebase User object (non-serializable)
  const [firebaseUser, setFirebaseUser] = useState<User | null>(null);

  // Local loading state for context compatibility
  // Start with true to prevent hydration mismatches
  const [isLoading, setIsLoading] = useState(true);

  // Track if we've completed the initial auth check
  const [hasInitialized, setHasInitialized] = useState(false);

  // Manual login function (for programmatic login - kept for backward compatibility)
  const login = () => dispatch(setIsAuthenticated(true));

  // Logout function that uses Redux thunk
  const logout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();
    } catch (error) {
      console.error("Error during logout:", error);
      // Force clear state even if logout fails
      dispatch(clearAuth());
    }
  };

  useEffect(() => {
    // Check auth status on mount
    dispatch(checkAuthStatus());

    // Listen to Firebase auth state changes
    const unsubscribe = lcFbOnAuthStateChanged(
      lcFbAuth,
      (user: User | null) => {
        // Store Firebase user locally (non-serializable)
        setFirebaseUser(user);

        // Update Redux state with serializable user data
        dispatch(setUser(user));
        dispatch(setIsAuthenticated(!!user));
        dispatch(setIsAuthLoading(false));

        // Mark as initialized and stop loading
        setHasInitialized(true);
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [dispatch]);

  // Sync local loading state with Redux
  useEffect(() => {
    setIsLoading(isAuthLoading);
  }, [isAuthLoading]);

  const contextValue = {
    isAuthenticated,
    isLoading,
    user: firebaseUser, // Provide the Firebase User object for backward compatibility
    login,
    logout
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export default AuthProvider;
