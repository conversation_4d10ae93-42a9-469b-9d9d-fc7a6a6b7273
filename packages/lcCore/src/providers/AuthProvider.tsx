"use client";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { lcFbAuth, lcFbOnAuthStateChanged } from "@/firebase";
import { setUser, setIsAuthenticated, setIsAuthLoading, clearAuth } from "@/store/features/auth/authSlice";
import { logoutUser, checkAuthStatus } from "@/store/features/auth/authThunks";
import type { User } from "firebase/auth";
import type { RootState, AppDispatch } from "@/store";

type AuthContextType = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: () => void;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function AuthProvider({ children }: { children: ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();

  // Get auth state from Redux
  const { user, isAuthenticated, isAuthLoading } = useSelector((state: RootState) => state.auth);

  // Local loading state for context compatibility
  const [isLoading, setIsLoading] = useState(true);

  // Manual login function (for programmatic login - kept for backward compatibility)
  const login = () => dispatch(setIsAuthenticated(true));

  // Logout function that uses Redux thunk
  const logout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();
    } catch (error) {
      console.error("Error during logout:", error);
      // Force clear state even if logout fails
      dispatch(clearAuth());
    }
  };

  useEffect(() => {
    // Check auth status on mount
    dispatch(checkAuthStatus());

    // Listen to Firebase auth state changes
    const unsubscribe = lcFbOnAuthStateChanged(
      lcFbAuth,
      (firebaseUser: User | null) => {
        // Update Redux state with Firebase user
        dispatch(setUser(firebaseUser));
        dispatch(setIsAuthenticated(!!firebaseUser));
        dispatch(setIsAuthLoading(false));
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [dispatch]);

  // Sync local loading state with Redux
  useEffect(() => {
    setIsLoading(isAuthLoading);
  }, [isAuthLoading]);

  const contextValue = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export default AuthProvider;
