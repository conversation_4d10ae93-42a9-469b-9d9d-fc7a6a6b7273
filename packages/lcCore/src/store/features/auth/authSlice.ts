import { createAppSlice } from "@/store/createAppSlice";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { User } from "firebase/auth";

/**
 * Authentication data structure containing user credentials and tokens
 * Matches the IAuthA interface from the Pinia store
 */
export interface IAuthData {
  /** Firebase authentication token */
  firebaseToken: string;
  /** API key for authentication */
  apiKey: string;
  /** Unique identifier for the user */
  userId: string;
  /** Unique identifier for the user's company */
  companyId: string;
  /** JSON Web Token for authentication */
  jwt: string;
  /** Refresh token for JWT */
  refreshJwt: string;
  /** Authentication token */
  authToken: string;
  /** Token used to refresh authentication */
  refreshToken: string;
}

/**
 * Login data structure for form submission and OTP verification
 */
export interface ILoginData {
  /** User's email address */
  email: string;
  /** User's password */
  password: string;
  /** Optional OTP channel */
  otpChannel?: "email" | "phone";
  /** Optional OTP code */
  otp?: string;
  /** Authentication token */
  token?: string;
  /** Device identifier */
  deviceId?: string;
  /** Name of the device */
  deviceName?: string;
  /** Type of device */
  deviceType?: string;
  /** Authentication domain */
  domain?: string;
  /** Company identifier */
  companyId?: string;
  /** Subdomain information */
  subdomain?: string;
}

/**
 * Enhanced authentication state that includes all functionality from Pinia store
 */
export interface AuthSliceState {
  /** Firebase user object */
  user: User | null;
  /** Whether user is authenticated */
  isAuthenticated: boolean;
  /** Whether authentication is currently loading */
  isAuthLoading: boolean;
  /** Current authentication page (login or OTP) */
  currentPage: "login" | "otp";
  /** Whether a login/authentication process is in progress */
  isLoading: boolean;
  /** Any login-related error messages */
  loginError: string | null;
  /** Comprehensive login data storage */
  loginData: ILoginData | null;
  /** Storage for authentication data */
  authData: IAuthData | null;
}

const initialState: AuthSliceState = {
  user: null,
  isAuthenticated: false,
  isAuthLoading: true,
  currentPage: "login",
  isLoading: false,
  loginError: null,
  loginData: null,
  authData: null,
};

const authSlice = createAppSlice({
  name: "auth",
  initialState,
  reducers: (create) => ({
    /**
     * Set Firebase user object
     */
    setUser: create.reducer((state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
    }),

    /**
     * Set authentication status
     */
    setIsAuthenticated: create.reducer(
      (state, action: PayloadAction<boolean>) => {
        state.isAuthenticated = action.payload;
      }
    ),

    /**
     * Set authentication loading state
     */
    setIsAuthLoading: create.reducer(
      (state, action: PayloadAction<boolean>) => {
        state.isAuthLoading = action.payload;
      }
    ),

    /**
     * Set current authentication page
     */
    setCurrentPage: create.reducer(
      (state, action: PayloadAction<"login" | "otp">) => {
        state.currentPage = action.payload;
      }
    ),

    /**
     * Set loading state for login/authentication process
     */
    setIsLoading: create.reducer(
      (state, action: PayloadAction<boolean>) => {
        state.isLoading = action.payload;
      }
    ),

    /**
     * Set login error message
     */
    setLoginError: create.reducer(
      (state, action: PayloadAction<string | null>) => {
        state.loginError = action.payload;
      }
    ),

    /**
     * Set login data
     */
    setLoginData: create.reducer(
      (state, action: PayloadAction<ILoginData | null>) => {
        state.loginData = action.payload;
      }
    ),

    /**
     * Set authentication data (equivalent to 'a' in Pinia store)
     */
    setAuthData: create.reducer(
      (state, action: PayloadAction<IAuthData | null>) => {
        state.authData = action.payload;
      }
    ),

    /**
     * Set both auth data and user (equivalent to setAuth in Pinia store)
     */
    setAuth: create.reducer(
      (state, action: PayloadAction<{ authData: IAuthData | null; user: User | null }>) => {
        state.authData = action.payload.authData;
        state.user = action.payload.user;
        state.isAuthLoading = false;
      }
    ),

    /**
     * Clear all authentication state (for logout)
     */
    clearAuth: create.reducer((state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.authData = null;
      state.loginData = null;
      state.loginError = null;
      state.currentPage = "login";
      state.isLoading = false;
    }),
  }),
});

export const {
  setUser,
  setIsAuthenticated,
  setIsAuthLoading,
  setCurrentPage,
  setIsLoading,
  setLoginError,
  setLoginData,
  setAuthData,
  setAuth,
  clearAuth,
} = authSlice.actions;

export default authSlice;
