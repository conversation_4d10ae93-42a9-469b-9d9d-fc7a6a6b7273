import { createAsyncThunk } from "@reduxjs/toolkit";
import { lcFbSignInWithCustomToken, lcFbAuth } from "@/firebase";
import type { RootState } from "@/store";
import type { ILoginFormPayload, LoginResponse, OtpResponse } from "./authApiSlice";
import { 
  setIsLoading, 
  setLoginError, 
  setLoginData, 
  setCurrentPage, 
  setAuth,
  clearAuth,
  type IAuthData,
  type ILoginData
} from "./authSlice";

/**
 * Async thunk for handling initial login form submission
 */
export const submitLoginForm = createAsyncThunk<
  LoginResponse,
  Omit<ILoginFormPayload, "deviceId" | "deviceName" | "deviceType" | "domain">,
  { state: RootState }
>(
  "auth/submitLoginForm",
  async (payload, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setIsLoading(true));
      dispatch(setLoginError(null));
      dispatch(setLoginData(null));

      // Store login data for OTP step
      const loginData: ILoginData = {
        ...payload,
      };
      dispatch(setLoginData(loginData));

      // Make API call using fetch (since RTK Query hooks can't be used in thunks)
      const response = await fetch("https://backend.leadconnectorhq.com/oauth/2/login/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json, text/plain, */*",
          "accept-language": "en-US,en;q=0.9",
          source: "WEB_USER",
          channel: "APP",
          version: "2021-07-28",
        },
        body: JSON.stringify({
          ...payload,
          // Add device info here if needed
        }),
      });

      const result: LoginResponse = await response.json();

      if (result.error) {
        dispatch(setLoginError(result.message || "Login failed. Please try again."));
        return rejectWithValue(result.message || "Login failed");
      }

      if (result.token) {
        // Update login data with token
        dispatch(setLoginData({ ...loginData, token: result.token }));
        dispatch(setCurrentPage("otp"));
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Login failed. Please try again.";
      dispatch(setLoginError(errorMessage));
      return rejectWithValue(errorMessage);
    } finally {
      dispatch(setIsLoading(false));
    }
  }
);

/**
 * Async thunk for handling OTP verification
 */
export const submitOtp = createAsyncThunk<
  OtpResponse,
  string,
  { state: RootState }
>(
  "auth/submitOtp",
  async (otpCode, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setIsLoading(true));
      dispatch(setLoginError(null));

      const state = getState();
      const loginData = state.auth.loginData;

      if (!loginData) {
        throw new Error("No login data available");
      }

      const payload = {
        ...loginData,
        otp: otpCode,
      };

      // Make API call
      const response = await fetch("https://backend.leadconnectorhq.com/oauth/2/login/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json, text/plain, */*",
          "accept-language": "en-US,en;q=0.9",
          source: "WEB_USER",
          channel: "APP",
          version: "2021-07-28",
        },
        body: JSON.stringify(payload),
      });

      const result: OtpResponse = await response.json();

      if (result.error) {
        dispatch(setLoginError(result.message || "OTP verification failed. Please try again."));
        return rejectWithValue(result.message || "OTP verification failed");
      }

      // Extract authentication data
      const {
        apiKey,
        userId,
        companyId,
        token: firebaseToken,
        jwt,
        refreshJwt,
        authToken,
        refreshToken,
      } = result;

      // Create auth data object
      const authData: IAuthData = {
        firebaseToken,
        apiKey,
        userId,
        companyId,
        jwt,
        refreshJwt,
        authToken,
        refreshToken,
      };

      // Store in cookies and localStorage (matching Pinia implementation)
      const cookieData = btoa(JSON.stringify({ apiKey, userId, companyId }));
      document.cookie = `a=${cookieData}; path=/`;
      
      const localStorageData = btoa(JSON.stringify(authData));
      localStorage.setItem("a", `"${localStorageData}"`);
      localStorage.setItem("loginDate", `"${new Date().toISOString()}"`);

      // Sign in with Firebase
      const userCredential = await lcFbSignInWithCustomToken(lcFbAuth, firebaseToken);
      const user = userCredential.user;

      // Update Redux state
      dispatch(setAuth({ authData, user }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "OTP verification failed. Please try again.";
      dispatch(setLoginError(errorMessage));
      return rejectWithValue(errorMessage);
    } finally {
      dispatch(setIsLoading(false));
    }
  }
);

/**
 * Async thunk for logout
 */
export const logoutUser = createAsyncThunk<
  void,
  void,
  { state: RootState }
>(
  "auth/logout",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState();
      const authData = state.auth.authData;

      // Call logout API if we have auth token
      if (authData?.authToken) {
        try {
          await fetch("https://backend.leadconnectorhq.com/oauth/2/logout", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${authData.authToken}`,
            },
          });
        } catch (error) {
          console.warn("Logout API call failed:", error);
          // Continue with local logout even if API call fails
        }
      }

      // Clear local storage
      localStorage.removeItem("a");
      localStorage.removeItem("loginDate");
      
      // Clear cookies
      document.cookie = "a=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

      // Sign out from Firebase
      const { lcFbSignOut } = await import("@/firebase");
      await lcFbSignOut(lcFbAuth);

      // Clear Redux state
      dispatch(clearAuth());
    } catch (error) {
      console.error("Logout error:", error);
      // Force clear state even if logout fails
      dispatch(clearAuth());
    }
  }
);

/**
 * Async thunk for checking authentication status on app load
 */
export const checkAuthStatus = createAsyncThunk<
  void,
  void,
  { state: RootState }
>(
  "auth/checkAuthStatus",
  async (_, { dispatch }) => {
    try {
      // Try to load auth data from localStorage
      const storedAuth = localStorage.getItem("a");
      if (storedAuth) {
        try {
          const parsedAuth = JSON.parse(storedAuth);
          const authData: IAuthData = JSON.parse(atob(parsedAuth));
          
          // Verify the token is still valid by checking Firebase auth state
          // The Firebase auth state listener in AuthProvider will handle the rest
          if (authData.firebaseToken) {
            const userCredential = await lcFbSignInWithCustomToken(lcFbAuth, authData.firebaseToken);
            // The AuthProvider will handle the user state update
          }
        } catch (error) {
          console.warn("Failed to restore auth from localStorage:", error);
          // Clear invalid data
          localStorage.removeItem("a");
          localStorage.removeItem("loginDate");
        }
      }
    } catch (error) {
      console.error("Auth status check failed:", error);
    }
  }
);

export default {
  submitLoginForm,
  submitOtp,
  logoutUser,
  checkAuthStatus,
};
