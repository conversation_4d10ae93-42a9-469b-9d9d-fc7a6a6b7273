import { create<PERSON>pi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { getLCRequestCommonHeaders, getDevice, getFirstZodError } from "@/utils";
import * as z from "zod";

/**
 * Zod schema for login form validation
 */
const loginFormPayloadSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
  otpChannel: z.enum(["email", "phone"]).optional(),
  otp: z.string().length(6, "Invalid OTP code").optional(),
  token: z.string().optional(),
  deviceId: z.string(),
  deviceName: z.string().optional(),
  deviceType: z.string().optional(),
  domain: z.string().optional(),
  companyId: z.string().optional(),
  subdomain: z.string().optional(),
});

export type ILoginFormPayload = z.infer<typeof loginFormPayloadSchema>;

/**
 * Response structure for the initial login attempt
 */
export interface LoginResponse {
  error: boolean;
  message?: string;
  token?: string;
}

/**
 * Comprehensive response structure after successful OTP verification
 */
export interface OtpResponse {
  error: boolean;
  message?: string;
  status: number;
  token: string;
  apiKey: string;
  userId: string;
  companyId: string;
  role: string;
  type: string;
  name: string;
  permissions: {
    workflowsEnabled: boolean;
    workflowsReadOnly: boolean;
  };
  authToken: string;
  refreshToken: string;
  jwt: string;
  refreshJwt: string;
}

/**
 * RTK Query API slice for authentication endpoints
 */
export const authApiSlice = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: "https://backend.leadconnectorhq.com",
    prepareHeaders: (headers) => {
      const commonHeaders = getLCRequestCommonHeaders();
      Object.entries(commonHeaders).forEach(([key, value]) => {
        headers.set(key, value);
      });
      return headers;
    },
  }),
  tagTypes: ["Auth"],
  endpoints: (builder) => ({
    /**
     * Initial login endpoint - sends email/password and receives token for OTP
     */
    submitLogin: builder.mutation<LoginResponse, Omit<ILoginFormPayload, "deviceId" | "deviceName" | "deviceType" | "domain">>({
      query: (credentials) => {
        // Add device information to the payload
        const deviceInfo = getDevice();
        const payload = {
          ...credentials,
          ...deviceInfo,
        };

        // Validate payload
        const parsedPayload = loginFormPayloadSchema.safeParse(payload);
        if (!parsedPayload.success) {
          throw new Error(getFirstZodError(parsedPayload));
        }

        return {
          url: "/oauth/2/login/email",
          method: "POST",
          body: payload,
        };
      },
      transformErrorResponse: (response: any) => {
        return {
          error: true,
          message: response.data?.message || "Login failed. Please try again.",
        };
      },
    }),

    /**
     * OTP verification endpoint - sends OTP code and receives full authentication data
     */
    submitOtp: builder.mutation<OtpResponse, ILoginFormPayload>({
      query: (payload) => {
        // Validate payload
        const parsedPayload = loginFormPayloadSchema.safeParse(payload);
        if (!parsedPayload.success) {
          throw new Error(getFirstZodError(parsedPayload));
        }

        return {
          url: "/oauth/2/login/email",
          method: "POST",
          body: payload,
        };
      },
      transformErrorResponse: (response: any) => {
        return {
          error: true,
          message: response.data?.message || "OTP verification failed. Please try again.",
        };
      },
    }),

    /**
     * Token refresh endpoint
     */
    refreshToken: builder.mutation<OtpResponse, { refreshToken: string }>({
      query: ({ refreshToken }) => ({
        url: "/oauth/2/refresh",
        method: "POST",
        body: { refreshToken },
      }),
      transformErrorResponse: (response: any) => {
        return {
          error: true,
          message: response.data?.message || "Token refresh failed. Please login again.",
        };
      },
    }),

    /**
     * Logout endpoint
     */
    logout: builder.mutation<{ success: boolean }, { authToken: string }>({
      query: ({ authToken }) => ({
        url: "/oauth/2/logout",
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      }),
      transformErrorResponse: (response: any) => {
        return {
          error: true,
          message: response.data?.message || "Logout failed.",
        };
      },
    }),
  }),
});

export const {
  useSubmitLoginMutation,
  useSubmitOtpMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
} = authApiSlice;

export default authApiSlice;
