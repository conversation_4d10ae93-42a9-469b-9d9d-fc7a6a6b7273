
"use client";
import { useAuth } from "@gd/core";

export default function Home() {
  const { user, logout, isAuthenticated } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="border-b border-gray-200 pb-4 mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2">Welcome to your dashboard</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Authentication Status */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-green-800 mb-2">
                Authentication Status
              </h2>
              <div className="space-y-2">
                <p className="text-sm">
                  <span className="font-medium">Status:</span>{" "}
                  <span className={`px-2 py-1 rounded text-xs ${
                    isAuthenticated
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                  </span>
                </p>
                {user && (
                  <>
                    <p className="text-sm">
                      <span className="font-medium">User ID:</span> {user.uid}
                    </p>
                    {user.email && (
                      <p className="text-sm">
                        <span className="font-medium">Email:</span> {user.email}
                      </p>
                    )}
                    {user.displayName && (
                      <p className="text-sm">
                        <span className="font-medium">Name:</span> {user.displayName}
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-800 mb-2">
                Actions
              </h2>
              <div className="space-y-3">
                <button
                  onClick={handleLogout}
                  className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  Sign Out
                </button>
                <p className="text-xs text-blue-600">
                  Signing out will redirect you to the login page
                </p>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-2">
              Authentication Flow Test
            </h2>
            <div className="text-sm text-gray-600 space-y-1">
              <p>✅ This page is protected by AuthGuard</p>
              <p>✅ You were automatically redirected here after login</p>
              <p>✅ If you sign out, you&apos;ll be redirected to login</p>
              <p>✅ Direct access to this page without auth redirects to login</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
